<?php

namespace App\Models;

use CodeIgniter\Model;

class CommodityModel extends Model
{
    protected $table = 'commodities';
    protected $primaryKey = 'commodity_id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    protected $allowedFields = [
        'commodity_name',
        'unit_of_measurement',
        'remarks',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Custom soft delete field
    protected $deletedFieldValue = true;
    protected $deletedFieldName = 'is_deleted';

    protected $validationRules = [
        'commodity_name' => 'required|min_length[2]|max_length[255]',
        'unit_of_measurement' => 'required|in_list[kg,g,lb,ton,liter,ml,gallon,piece,unit,box,carton,pack]',
        'remarks' => 'permit_empty|max_length[1000]'
    ];

    protected $validationMessages = [
        'commodity_name' => [
            'required' => 'Commodity name is required',
            'min_length' => 'Commodity name must be at least 2 characters long',
            'max_length' => 'Commodity name cannot exceed 255 characters'
        ],
        'unit_of_measurement' => [
            'required' => 'Unit of measurement is required',
            'in_list' => 'Please select a valid unit of measurement'
        ],
        'remarks' => [
            'max_length' => 'Remarks cannot exceed 1000 characters'
        ]
    ];

    /**
     * Get available units of measurement
     */
    public function getUnitsOfMeasurement(): array
    {
        return [
            'kg' => 'Kilogram (kg)',
            'g' => 'Gram (g)',
            'lb' => 'Pound (lb)',
            'ton' => 'Ton',
            'liter' => 'Liter',
            'ml' => 'Milliliter (ml)',
            'gallon' => 'Gallon',
            'piece' => 'Piece',
            'unit' => 'Unit',
            'box' => 'Box',
            'carton' => 'Carton',
            'pack' => 'Pack'
        ];
    }

    /**
     * Find commodity by name
     */
    public function findByName(string $name)
    {
        return $this->where('LOWER(commodity_name)', strtolower($name))->first();
    }

    /**
     * Create a new commodity
     */
    public function createCommodity(array $data, int $createdBy = null): bool
    {
        if ($createdBy) {
            $data['created_by'] = $createdBy;
            $data['updated_by'] = $createdBy;
        }

        return $this->insert($data);
    }

    /**
     * Update commodity data
     */
    public function updateCommodity(int $id, array $data, int $updatedBy = null): bool
    {
        if ($updatedBy) {
            $data['updated_by'] = $updatedBy;
        }

        return $this->update($id, $data);
    }

    /**
     * Delete commodity (soft delete)
     */
    public function deleteCommodity(int $id, int $deletedBy = null): bool
    {
        $data = ['is_deleted' => true];
        if ($deletedBy) {
            $data['deleted_by'] = $deletedBy;
        }
        
        return $this->update($id, $data);
    }

    /**
     * Get active commodities
     */
    public function getActiveCommodities(int $limit = 20, int $offset = 0): array
    {
        return $this->where('is_deleted', false)
                   ->orderBy('commodity_name', 'ASC')
                   ->findAll($limit, $offset);
    }

    /**
     * Search commodities by name
     */
    public function searchCommodities(string $search, int $limit = 20): array
    {
        return $this->where('is_deleted', false)
                   ->like('commodity_name', $search)
                   ->orderBy('commodity_name', 'ASC')
                   ->findAll($limit);
    }

    /**
     * Get commodities by unit of measurement
     */
    public function getCommoditiesByUnit(string $unit, int $limit = 20): array
    {
        return $this->where('is_deleted', false)
                   ->where('unit_of_measurement', $unit)
                   ->orderBy('commodity_name', 'ASC')
                   ->findAll($limit);
    }

    /**
     * Get commodity statistics
     */
    public function getCommodityStats(): array
    {
        $total = $this->where('is_deleted', false)->countAllResults();
        
        $unitStats = $this->select('unit_of_measurement, COUNT(*) as count')
                          ->where('is_deleted', false)
                          ->groupBy('unit_of_measurement')
                          ->orderBy('count', 'DESC')
                          ->findAll();

        return [
            'total_commodities' => $total,
            'unit_distribution' => $unitStats
        ];
    }

    /**
     * Check if commodity name exists (for validation)
     */
    public function commodityNameExists(string $name, int $excludeId = null): bool
    {
        $query = $this->where('LOWER(commodity_name)', strtolower($name))
                     ->where('is_deleted', false);
        
        if ($excludeId) {
            $query->where('commodity_id !=', $excludeId);
        }
        
        return $query->countAllResults() > 0;
    }

    /**
     * Get commodities with pagination and search
     */
    public function getCommoditiesPaginated(int $perPage = 20, string $search = ''): array
    {
        $query = $this->where('is_deleted', false);
        
        if (!empty($search)) {
            $query->like('commodity_name', $search);
        }
        
        return $query->orderBy('commodity_name', 'ASC')
                    ->paginate($perPage);
    }

    /**
     * Restore deleted commodity
     */
    public function restoreCommodity(int $id, int $restoredBy = null): bool
    {
        $data = [
            'is_deleted' => false,
            'deleted_at' => null,
            'deleted_by' => null
        ];
        
        if ($restoredBy) {
            $data['updated_by'] = $restoredBy;
        }
        
        return $this->update($id, $data);
    }
}
