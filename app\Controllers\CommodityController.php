<?php

namespace App\Controllers;

use App\Models\CommodityModel;

class CommodityController extends BaseController
{
    protected $commodityModel;
    protected $data = [];

    public function __construct()
    {
        $this->commodityModel = new CommodityModel();
        
        // Initialize common admin data
        $this->data['user_name'] = session('fullname') ?? session('username') ?? 'User';
        $this->data['user_email'] = session('email') ?? '<EMAIL>';
        $this->data['user_role'] = session('is_admin') ? 'administrator' : (session('is_supervisor') ? 'supervisor' : 'user');
    }

    /**
     * Display list of commodities (GET)
     */
    public function index()
    {
        $search = $this->request->getGet('search') ?? '';
        $perPage = 20;
        
        // Get commodities with pagination and search
        $commodities = $this->commodityModel->getCommoditiesPaginated($perPage, $search);
        $pager = $this->commodityModel->pager;
        
        // Get commodity statistics
        $stats = $this->commodityModel->getCommodityStats();

        $this->data['title'] = 'Commodity Management - DCBuyer Admin';
        $this->data['active_menu'] = 'commodities';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Commodities', 'url' => base_url('admin/commodities')]
        ];
        
        $this->data['page_title'] = 'Commodity Management';
        $this->data['page_description'] = 'Manage all commodities available for trading on your platform.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/commodities/create') . '" class="btn btn-admin-primary">
                <i class="fas fa-plus me-2"></i>Add New Commodity
            </a>
        ';
        
        $this->data['commodities'] = $commodities;
        $this->data['pager'] = $pager;
        $this->data['search'] = $search;
        $this->data['stats'] = $stats;

        return view('admin/commodities/index', $this->data);
    }

    /**
     * Show create commodity form (GET)
     */
    public function create()
    {
        $this->data['title'] = 'Create Commodity - DCBuyer Admin';
        $this->data['active_menu'] = 'commodities';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Commodities', 'url' => base_url('admin/commodities')],
            ['title' => 'Create Commodity', 'url' => base_url('admin/commodities/create')]
        ];

        $this->data['page_title'] = 'Create New Commodity';
        $this->data['page_description'] = 'Add a new commodity to the trading platform.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/commodities') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Commodities
            </a>
        ';

        $this->data['units'] = $this->commodityModel->getUnitsOfMeasurement();

        return view('admin/commodities/create', $this->data);
    }

    /**
     * Store new commodity (POST)
     */
    public function store()
    {
        $rules = [
            'commodity_name' => 'required|min_length[2]|max_length[255]',
            'unit_of_measurement' => 'required|in_list[kg,g,lb,ton,liter,ml,gallon,piece,unit,box,carton,pack]',
            'remarks' => 'permit_empty|max_length[1000]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // Check if commodity name already exists
        $existingCommodity = $this->commodityModel->findByName($this->request->getPost('commodity_name'));
        if ($existingCommodity) {
            return redirect()->back()->withInput()->with('error', 'A commodity with this name already exists.');
        }
        
        $data = [
            'commodity_name' => $this->request->getPost('commodity_name'),
            'unit_of_measurement' => $this->request->getPost('unit_of_measurement'),
            'remarks' => $this->request->getPost('remarks')
        ];
        
        try {
            $result = $this->commodityModel->createCommodity($data, session()->get('user_id'));
            
            if ($result) {
                return redirect()->to('admin/commodities')->with('success', 'Commodity created successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to create commodity.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error creating commodity: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while creating the commodity.');
        }
    }

    /**
     * Show commodity details (GET)
     */
    public function show($id)
    {
        $commodity = $this->commodityModel->find($id);

        if (!$commodity) {
            return redirect()->to('admin/commodities')->with('error', 'Commodity not found.');
        }

        $this->data['title'] = 'Commodity Details - DCBuyer Admin';
        $this->data['active_menu'] = 'commodities';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Commodities', 'url' => base_url('admin/commodities')],
            ['title' => $commodity['commodity_name'], 'url' => base_url('admin/commodities/' . $id)]
        ];

        $this->data['page_title'] = $commodity['commodity_name'];
        $this->data['page_description'] = 'View commodity details and information.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/commodities/' . $id . '/edit') . '" class="btn btn-admin-primary">
                <i class="fas fa-edit me-2"></i>Edit Commodity
            </a>
            <a href="' . base_url('admin/commodities') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Commodities
            </a>
        ';

        $this->data['commodity'] = $commodity;
        $this->data['units'] = $this->commodityModel->getUnitsOfMeasurement();

        return view('admin/commodities/show', $this->data);
    }

    /**
     * Show edit commodity form (GET)
     */
    public function edit($id)
    {
        $commodity = $this->commodityModel->find($id);

        if (!$commodity) {
            return redirect()->to('admin/commodities')->with('error', 'Commodity not found.');
        }

        $this->data['title'] = 'Edit Commodity - DCBuyer Admin';
        $this->data['active_menu'] = 'commodities';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Commodities', 'url' => base_url('admin/commodities')],
            ['title' => $commodity['commodity_name'], 'url' => base_url('admin/commodities/' . $id)],
            ['title' => 'Edit', 'url' => base_url('admin/commodities/' . $id . '/edit')]
        ];

        $this->data['page_title'] = 'Edit Commodity';
        $this->data['page_description'] = 'Update commodity information.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/commodities/' . $id) . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Details
            </a>
        ';

        $this->data['commodity'] = $commodity;
        $this->data['units'] = $this->commodityModel->getUnitsOfMeasurement();

        return view('admin/commodities/edit', $this->data);
    }

    /**
     * Update commodity (PUT/PATCH)
     */
    public function update($id)
    {
        $commodity = $this->commodityModel->find($id);

        if (!$commodity) {
            return redirect()->to('admin/commodities')->with('error', 'Commodity not found.');
        }
        
        $rules = [
            'commodity_name' => 'required|min_length[2]|max_length[255]',
            'unit_of_measurement' => 'required|in_list[kg,g,lb,ton,liter,ml,gallon,piece,unit,box,carton,pack]',
            'remarks' => 'permit_empty|max_length[1000]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // Check if commodity name already exists (excluding current commodity)
        if ($this->commodityModel->commodityNameExists($this->request->getPost('commodity_name'), $id)) {
            return redirect()->back()->withInput()->with('error', 'A commodity with this name already exists.');
        }
        
        $data = [
            'commodity_name' => $this->request->getPost('commodity_name'),
            'unit_of_measurement' => $this->request->getPost('unit_of_measurement'),
            'remarks' => $this->request->getPost('remarks')
        ];
        
        try {
            $result = $this->commodityModel->updateCommodity($id, $data, session()->get('user_id'));
            
            if ($result) {
                return redirect()->to('admin/commodities')->with('success', 'Commodity updated successfully.');
            } else {
                $errors = $this->commodityModel->errors();
                return redirect()->back()->withInput()->with('error', 'Failed to update commodity. ' . implode(', ', $errors));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error updating commodity: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating the commodity.');
        }
    }

    /**
     * Delete commodity (DELETE)
     */
    public function delete($id)
    {
        $commodity = $this->commodityModel->find($id);

        if (!$commodity) {
            return redirect()->to('admin/commodities')->with('error', 'Commodity not found.');
        }

        try {
            $result = $this->commodityModel->deleteCommodity($id, session()->get('user_id'));
            
            if ($result) {
                return redirect()->to('admin/commodities')->with('success', 'Commodity deleted successfully.');
            } else {
                return redirect()->to('admin/commodities')->with('error', 'Failed to delete commodity.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error deleting commodity: ' . $e->getMessage());
            return redirect()->to('admin/commodities')->with('error', 'An error occurred while deleting the commodity.');
        }
    }
}
