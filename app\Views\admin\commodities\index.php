<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .stats-card {
        background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
        border-radius: 12px;
        padding: 1.5rem;
        color: white;
        margin-bottom: 1.5rem;
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .commodity-table {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .table-header {
        background: #f8f9fa;
        padding: 1.5rem;
        border-bottom: 1px solid #dee2e6;
    }
    
    .search-box {
        max-width: 300px;
    }
    
    .unit-badge {
        background: #e3f2fd;
        color: #1976d2;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }
    
    .action-buttons .btn {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    .table th {
        font-weight: 600;
        color: #495057;
        border-bottom: 2px solid #dee2e6;
    }
    
    .table td {
        vertical-align: middle;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number"><?= $stats['total_commodities'] ?></div>
                <div class="stats-label">Total Commodities</div>
            </div>
        </div>
        <div class="col-md-9">
            <div class="stats-card">
                <h6 class="mb-3">Unit Distribution</h6>
                <div class="row">
                    <?php foreach (array_slice($stats['unit_distribution'], 0, 4) as $unit): ?>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h5 mb-1"><?= $unit['count'] ?></div>
                            <small><?= strtoupper($unit['unit_of_measurement']) ?></small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Commodities Table -->
    <div class="commodity-table">
        <div class="table-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0">Commodities List</h5>
                    <small class="text-muted">Manage all commodities in your platform</small>
                </div>
                <div class="col-md-6">
                    <form method="GET" class="d-flex justify-content-end">
                        <div class="search-box">
                            <div class="input-group">
                                <input type="text" class="form-control" name="search" 
                                       placeholder="Search commodities..." value="<?= esc($search) ?>">
                                <button class="btn btn-outline-secondary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <?php if (empty($commodities)): ?>
        <div class="empty-state">
            <i class="fas fa-seedling"></i>
            <h5>No Commodities Found</h5>
            <p class="mb-3">
                <?php if ($search): ?>
                    No commodities match your search criteria.
                <?php else: ?>
                    Start by adding your first commodity to the platform.
                <?php endif; ?>
            </p>
            <a href="<?= base_url('admin/commodities/create') ?>" class="btn btn-admin-primary">
                <i class="fas fa-plus me-2"></i>Add First Commodity
            </a>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Commodity Name</th>
                        <th>Unit of Measurement</th>
                        <th>Remarks</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($commodities as $commodity): ?>
                    <tr>
                        <td>
                            <span class="fw-bold text-primary">#<?= $commodity['commodity_id'] ?></span>
                        </td>
                        <td>
                            <div class="fw-bold"><?= esc($commodity['commodity_name']) ?></div>
                        </td>
                        <td>
                            <span class="unit-badge"><?= strtoupper(esc($commodity['unit_of_measurement'])) ?></span>
                        </td>
                        <td>
                            <span class="text-muted">
                                <?= $commodity['remarks'] ? esc(substr($commodity['remarks'], 0, 50)) . (strlen($commodity['remarks']) > 50 ? '...' : '') : '-' ?>
                            </span>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?= date('M j, Y', strtotime($commodity['created_at'])) ?>
                            </small>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="<?= base_url('admin/commodities/' . $commodity['commodity_id']) ?>" 
                                   class="btn btn-sm btn-outline-primary" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?= base_url('admin/commodities/' . $commodity['commodity_id'] . '/edit') ?>" 
                                   class="btn btn-sm btn-outline-success" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="confirmDelete(<?= $commodity['commodity_id'] ?>, '<?= esc($commodity['commodity_name']) ?>')"
                                        title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($pager->getPageCount() > 1): ?>
        <div class="p-3 border-top">
            <?= $pager->links() ?>
        </div>
        <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the commodity <strong id="commodityName"></strong>?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete Commodity</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function confirmDelete(commodityId, commodityName) {
    document.getElementById('commodityName').textContent = commodityName;
    document.getElementById('deleteForm').action = '<?= base_url('admin/commodities') ?>/' + commodityId + '/delete';
    
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
<?= $this->endSection() ?>
