<?php

use CodeIgniter\Database\Migration;

class AddBagToUnitOfMeasurement extends Migration
{
    public function up()
    {
        // Add 'bag' to the existing unit_of_measurement ENUM type
        $this->db->query("ALTER TYPE unit_of_measurement ADD VALUE 'bag';");
    }

    public function down()
    {
        // Note: Removing ENUM values is not allowed if they're in use
        // Manual intervention required for rollback
        log_message('error', 'Migration rollback not supported: Cannot remove ENUM value "bag" from unit_of_measurement without data migration');
    }
}